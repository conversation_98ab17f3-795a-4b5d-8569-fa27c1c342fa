import { useEffect } from 'react';

/**
 * Lazy-loaded analytics provider that initializes PostHog only when needed
 * This reduces the initial bundle size by ~188KB
 */
export const AnalyticsProvider = () => {
  useEffect(() => {
    // Only load analytics after main content has loaded
    const loadAnalytics = async () => {
      // Don't load analytics in development unless explicitly enabled
      if (import.meta.env.DEV && !import.meta.env.VITE_ENABLE_DEV_ANALYTICS) {
        return;
      }

      // Check if required environment variables are available
      const posthogKey = import.meta.env.VITE_PUBLIC_POSTHOG_KEY;
      const posthogHost = import.meta.env.VITE_PUBLIC_POSTHOG_HOST;

      if (!posthogKey || !posthogHost) {
        if (import.meta.env.DEV) {
          console.warn('PostHog environment variables not set. Analytics disabled.');
        }
        return;
      }

      try {
        // Dynamically import PostHog to keep it out of the main bundle
        const { default: posthog } = await import('posthog-js');

        // Initialize with optimized configuration
        posthog.init(
          posthogKey,
          {
            api_host: posthogHost,
            // Only enable debug in development
            debug: import.meta.env.DEV,
            // Disable automatic page view tracking - we'll do it manually
            autocapture: false,
            // Only load core features to reduce bundle size
            disable_session_recording: true,
            disable_persistence: false,
            // Use performance-optimized settings
            capture_pageview: false,
            capture_pageleave: false,
            // Load callback
            loaded: (posthog) => {
              // Manually capture page view after initialization
              posthog.capture('$pageview');

              if (import.meta.env.DEV) {
                console.log('PostHog loaded successfully');
              }
            }
          }
        );
      } catch (error) {
        // Fail silently - analytics should not break the app
        if (import.meta.env.DEV) {
          console.error('Failed to load analytics:', error);
        }
      }
    };

    // Use requestIdleCallback to load analytics when browser is idle
    if ('requestIdleCallback' in window) {
      const handle = requestIdleCallback(() => loadAnalytics());
      return () => cancelIdleCallback(handle);
    } else {
      // Fallback: load after a delay
      const timer = setTimeout(loadAnalytics, 2000);
      return () => clearTimeout(timer);
    }
  }, []);

  return null;
};

export default AnalyticsProvider;