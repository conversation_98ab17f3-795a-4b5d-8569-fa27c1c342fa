/**
 * Deployment Health Check Component
 * 
 * This component helps diagnose deployment issues by checking:
 * - Environment variables
 * - Asset loading
 * - JavaScript execution
 * - CSS loading
 * 
 * Only shows in development or when explicitly enabled
 */

import { useEffect, useState } from 'react';

interface HealthCheckResult {
  envVars: boolean;
  cssLoaded: boolean;
  jsExecuting: boolean;
  assetsAccessible: boolean;
  errors: string[];
}

export const DeploymentHealthCheck = () => {
  const [healthCheck, setHealthCheck] = useState<HealthCheckResult>({
    envVars: false,
    cssLoaded: false,
    jsExecuting: true, // If this component renders, JS is executing
    assetsAccessible: false,
    errors: []
  });

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development or if explicitly enabled via URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const showHealthCheck = import.meta.env.DEV || urlParams.get('debug') === 'true';
    
    if (!showHealthCheck) return;

    setIsVisible(true);
    
    const runHealthCheck = async () => {
      const errors: string[] = [];
      let envVars = false;
      let cssLoaded = false;
      let assetsAccessible = false;

      // Check environment variables
      try {
        const posthogKey = import.meta.env.VITE_PUBLIC_POSTHOG_KEY;
        const posthogHost = import.meta.env.VITE_PUBLIC_POSTHOG_HOST;
        
        if (posthogKey && posthogHost) {
          envVars = true;
        } else {
          errors.push('Missing PostHog environment variables');
        }
      } catch (error) {
        errors.push(`Environment variable check failed: ${error}`);
      }

      // Check CSS loading
      try {
        const computedStyle = window.getComputedStyle(document.body);
        const backgroundColor = computedStyle.backgroundColor;
        
        // Check if our dark theme is applied
        if (backgroundColor.includes('0, 0, 0') || backgroundColor === 'rgb(0, 0, 0)') {
          cssLoaded = true;
        } else {
          errors.push('CSS not loaded properly - background color not applied');
        }
      } catch (error) {
        errors.push(`CSS check failed: ${error}`);
      }

      // Check asset accessibility
      try {
        const response = await fetch('/favicon/favicon.ico', { method: 'HEAD' });
        if (response.ok) {
          assetsAccessible = true;
        } else {
          errors.push('Assets not accessible - favicon check failed');
        }
      } catch (error) {
        errors.push(`Asset accessibility check failed: ${error}`);
        assetsAccessible = false;
      }

      setHealthCheck({
        envVars,
        cssLoaded,
        jsExecuting: true,
        assetsAccessible,
        errors
      });
    };

    runHealthCheck();
  }, []);

  if (!isVisible) return null;

  const allGood = healthCheck.envVars && healthCheck.cssLoaded && 
                  healthCheck.jsExecuting && healthCheck.assetsAccessible;

  return (
    <div 
      style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: allGood ? '#10b981' : '#ef4444',
        color: 'white',
        padding: '12px',
        borderRadius: '8px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 9999,
        maxWidth: '300px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
      }}
    >
      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
        🔍 Deployment Health Check
      </div>
      
      <div style={{ marginBottom: '4px' }}>
        {healthCheck.jsExecuting ? '✅' : '❌'} JavaScript Executing
      </div>
      <div style={{ marginBottom: '4px' }}>
        {healthCheck.cssLoaded ? '✅' : '❌'} CSS Loaded
      </div>
      <div style={{ marginBottom: '4px' }}>
        {healthCheck.envVars ? '✅' : '❌'} Environment Variables
      </div>
      <div style={{ marginBottom: '8px' }}>
        {healthCheck.assetsAccessible ? '✅' : '❌'} Assets Accessible
      </div>

      {healthCheck.errors.length > 0 && (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Errors:</div>
          {healthCheck.errors.map((error, index) => (
            <div key={index} style={{ fontSize: '10px', marginBottom: '2px' }}>
              • {error}
            </div>
          ))}
        </div>
      )}

      <div style={{ fontSize: '10px', marginTop: '8px', opacity: 0.8 }}>
        Add ?debug=true to URL to show in production
      </div>
    </div>
  );
};

export default DeploymentHealthCheck;
